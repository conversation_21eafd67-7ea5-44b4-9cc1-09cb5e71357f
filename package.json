{"name": "hello-renative", "devDependencies": {"@flexn/assets-renative-outline": "0.3.5", "@flexn/typescript-config": "1.0.0", "@rnv/adapter": "1.3.0", "@rnv/cli": "1.3.0", "@rnv/config-templates": "1.3.0", "@rnv/core": "1.3.0", "@rnv/engine-rn": "1.3.0", "@rnv/engine-rn-electron": "1.3.0", "@rnv/engine-rn-next": "1.3.0", "@rnv/engine-rn-tvos": "1.3.0", "@rnv/engine-rn-web": "1.3.0", "@rnv/template-starter": "1.3.0", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@types/react-native": "0.72.3", "babel-loader": "9.1.3", "dotenv": "16.4.5", "jetifier": "1.6.5", "minipass": "7.1.2", "readable-stream": "4.5.2", "typescript": "5.2.2", "xcode": "2.1.0"}, "browserslist": [">0.2%", "not op_mini all"], "scripts": {"build:androidtv": "npx rnv build -p androidtv"}, "version": "0.2.6", "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/native": "6.1.16", "@react-navigation/native-stack": "5.0.5", "@rnv/renative": "1.3.0", "build-url-ts": "^6.1.8", "core-js": "^3.39.0", "dotenv": "16.4.5", "next": "14.2.10", "raf": "3.4.1", "react": "18.3.1", "react-art": "18.3.1", "react-dom": "18.3.1", "react-native": "0.73.4", "react-native-gesture-handler": "2.16.2", "react-native-linear-gradient": "latest", "react-native-safe-area-context": "4.10.3", "react-native-screens": "3.29.0", "react-native-tvos": "0.73.6-0", "react-native-video": "6.1.0", "react-native-web": "0.19.12", "react-native-web-linear-gradient": "1.1.2", "rnv": "^0.37.4", "uuid": "^11.0.3"}}