import React, { useState, memo, useCallback } from "react";
import {
	View,
	Text,
	TextInput,
	TouchableOpacity,
	StyleSheet,
	ActivityIndicator,
	Platform,
	Pressable,
} from "react-native";
import { GLOBAL_STYLES } from "../styles/globalStyles";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../app/index";
import { scale } from "../utils/helpers/dimensionScale.helper";
import {
	AuthService,
	LoginCredentials,
} from "../services/authService";
import { QRCodeSection } from "../components/common";

/**
 * LoginPage Component
 * Displays login form alongside QR code for account management
 * Optimized with React.memo for performance
 */
const LoginPage = memo(() => {
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");
	// TV focus state management for input fields
	const [isEmailFocused, setIsEmailFocused] = useState(false);
	const [isPasswordFocused, setIsPasswordFocused] = useState(false);
	const [isButtonFocused, setIsButtonFocused] = useState(false);
	const navigation =
		useNavigation<NativeStackNavigationProp<RootStackParamList>>();

	// Memoized handlers for performance
	const handleEmailChange = useCallback(
		(text: string) => {
			setEmail(text);
			// Clear error when user starts typing
			if (error) setError("");
		},
		[error]
	);

	const handlePasswordChange = useCallback(
		(text: string) => {
			setPassword(text);
			// Clear error when user starts typing
			if (error) setError("");
		},
		[error]
	);

	// TV focus handlers for input fields
	const handleEmailFocus = useCallback(
		() => setIsEmailFocused(true),
		[]
	);
	const handleEmailBlur = useCallback(
		() => setIsEmailFocused(false),
		[]
	);
	const handlePasswordFocus = useCallback(
		() => setIsPasswordFocused(true),
		[]
	);
	const handlePasswordBlur = useCallback(
		() => setIsPasswordFocused(false),
		[]
	);
	const handleButtonFocus = useCallback(
		() => setIsButtonFocused(true),
		[]
	);
	const handleButtonBlur = useCallback(
		() => setIsButtonFocused(false),
		[]
	);

	// Handle TV input activation - when user presses OK/Enter on d-pad
	const handleEmailPress = useCallback(() => {
		// Focus the email input when the wrapper is pressed on TV
		console.log("Email wrapper pressed - activating input");
	}, []);

	const handlePasswordPress = useCallback(() => {
		// Focus the password input when the wrapper is pressed on TV
		console.log("Password wrapper pressed - activating input");
	}, []);

	/**
	 * Handle login form submission
	 * Validates input and calls authentication service
	 */
	const handleLogin = async () => {
		// Clear previous errors
		setError("");

		// Validate input
		if (!email.trim()) {
			setError("Email is required");
			return;
		}

		if (!AuthService.validateEmail(email)) {
			setError("Please enter a valid email address");
			return;
		}

		if (!password.trim()) {
			setError("Password is required");
			return;
		}

		if (!AuthService.validatePassword(password)) {
			setError("Password must be at least 1 characters long");
			return;
		}

		// Start loading
		setIsLoading(true);

		try {
			console.log("🔐 Starting login attempt for:", email);
			console.log("Password details:", {
				length: password.length,
				chars: password.split("").map((c) => c.charCodeAt(0)),
				raw: password,
			});
			const credentials: LoginCredentials = {
				email: email.trim(), // Only trim email
				password: password, // Use password as is
			};
			const result = await AuthService.login(credentials); // Use the main login method

			if (result.success) {
				// Login successful, navigate back to home page
				console.log(
					"✅ Login successful! Navigating back to HomePage"
				);
				console.log(
					"🏠 HomePage will re-check authentication status on focus"
				);
				navigation.goBack(); // Go back to previous screen (HomePage)
			} else {
				// Login failed, show error
				console.log("❌ Login failed:", result.error);
				setError(result.error || "Login failed. Please try again.");
			}
		} catch (error) {
			console.log("💥 Unexpected login error:", error);
			setError("An unexpected error occurred. Please try again.");
		} finally {
			// Stop loading
			setIsLoading(false);
		}
	};

	return (
		<View style={styles.pageBackground}>
			<View style={styles.mainContainer}>
				{/* Left side - Login Form */}
				<View style={styles.loginSection}>
					<View style={styles.card}>
						<Text style={styles.title}>LOG IN</Text>

						{/* Error message display */}
						{error ? (
							<View style={styles.errorContainer}>
								<Text style={styles.errorText}>{error}</Text>
							</View>
						) : null}

						<View style={styles.formGroup}>
							<Text style={styles.label}>Email</Text>
							{Platform.isTV ? (
								// TV platforms: Wrap TextInput in focusable Pressable for d-pad navigation
								<Pressable
									style={[
										styles.input,
										error ? styles.inputError : null,
										isEmailFocused && styles.inputFocused,
									]}
									onPress={handleEmailPress}
									onFocus={handleEmailFocus}
									onBlur={handleEmailBlur}
									accessible={true}
									accessibilityRole="button"
									accessibilityLabel="Email address input field"
								>
									<TextInput
										style={styles.tvTextInput}
										value={email}
										onChangeText={handleEmailChange}
										placeholder="Enter your email"
										placeholderTextColor={
											GLOBAL_STYLES.COLORS.TEXT_TERTIARY
										}
										autoCapitalize="none"
										keyboardType="email-address"
										editable={!isLoading}
										// Remove focus handling from TextInput on TV - handled by Pressable wrapper
										accessible={false}
									/>
								</Pressable>
							) : (
								// Mobile platforms: Use TextInput directly
								<TextInput
									style={[
										styles.input,
										error ? styles.inputError : null,
										isEmailFocused && styles.inputFocused,
									]}
									value={email}
									onChangeText={handleEmailChange}
									placeholder="Enter your email"
									placeholderTextColor={
										GLOBAL_STYLES.COLORS.TEXT_TERTIARY
									}
									autoCapitalize="none"
									keyboardType="email-address"
									editable={!isLoading}
									accessible={true}
									accessibilityRole="text"
									accessibilityLabel="Email address input field"
								/>
							)}
						</View>
						<View style={styles.formGroup}>
							<Text style={styles.label}>Password</Text>
							{Platform.isTV ? (
								// TV platforms: Wrap TextInput in focusable Pressable for d-pad navigation
								<Pressable
									style={[
										styles.input,
										error ? styles.inputError : null,
										isPasswordFocused && styles.inputFocused,
									]}
									onPress={handlePasswordPress}
									onFocus={handlePasswordFocus}
									onBlur={handlePasswordBlur}
									accessible={true}
									accessibilityRole="button"
									accessibilityLabel="Password input field"
								>
									<TextInput
										style={styles.tvTextInput}
										value={password}
										onChangeText={handlePasswordChange}
										placeholder="Enter your password"
										placeholderTextColor={
											GLOBAL_STYLES.COLORS.TEXT_TERTIARY
										}
										secureTextEntry
										editable={!isLoading}
										// Remove focus handling from TextInput on TV - handled by Pressable wrapper
										accessible={false}
									/>
								</Pressable>
							) : (
								// Mobile platforms: Use TextInput directly
								<TextInput
									style={[
										styles.input,
										error ? styles.inputError : null,
										isPasswordFocused && styles.inputFocused,
									]}
									value={password}
									onChangeText={handlePasswordChange}
									placeholder="Enter your password"
									placeholderTextColor={
										GLOBAL_STYLES.COLORS.TEXT_TERTIARY
									}
									secureTextEntry
									editable={!isLoading}
									accessible={true}
									accessibilityRole="text"
									accessibilityLabel="Password input field"
								/>
							)}
						</View>
						<TouchableOpacity
							style={[
								styles.button,
								{ opacity: isLoading ? 0.5 : 0.7 },
								isLoading && styles.buttonDisabled,
								isButtonFocused && styles.buttonFocused,
							]}
							onPress={handleLogin}
							activeOpacity={1}
							disabled={isLoading}
							// TV focus properties for d-pad navigation
							onFocus={handleButtonFocus}
							onBlur={handleButtonBlur}
							hasTVPreferredFocus={false}
							accessible={true}
							accessibilityRole="button"
							accessibilityLabel="Log in button"
						>
							{isLoading ? (
								<View style={styles.buttonContent}>
									<ActivityIndicator
										size="small"
										color={GLOBAL_STYLES.COLORS.TEXT_PRIMARY}
										style={styles.loadingIndicator}
									/>
									<Text style={styles.buttonText}>Logging in...</Text>
								</View>
							) : (
								<Text style={styles.buttonText}>Log in</Text>
							)}
						</TouchableOpacity>
					</View>
				</View>

				{/* Right side - QR Code Section */}
				<View style={styles.qrSection}>
					<QRCodeSection
						title="TO MANAGE ACCOUNT AND SUBSCRIPTION PLEASE FOLLOW THE QR CODE"
						showTitle={true}
					/>
				</View>
			</View>
		</View>
	);
});

const styles = StyleSheet.create({
	pageBackground: {
		flex: 1,
		backgroundColor: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
		justifyContent: "center",
		alignItems: "center",
	},
	mainContainer: {
		flexDirection: "row",
		alignItems: "center",
		justifyContent: "center",
		width: "100%",
		maxWidth: scale(1600),
		paddingHorizontal: scale(64),
	},
	loginSection: {
		flex: 1,
		alignItems: "center",
		justifyContent: "center",
		paddingRight: scale(80),
	},
	qrSection: {
		flex: 1,
		alignItems: "flex-start",
		justifyContent: "center",
		paddingLeft: scale(80),
	},
	card: {
		backgroundColor: "#131c2b",
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		padding: scale(80),
		minWidth: scale(700),
		width: "100%",
		maxWidth: scale(800),
		alignItems: "center",
		shadowOpacity: 0.2,
		shadowRadius: scale(8),
		elevation: scale(4),
	},
	title: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(64),
		fontWeight: "bold",
		marginBottom: scale(64),
		letterSpacing: scale(2),
	},
	formGroup: {
		width: "100%",
		marginBottom: scale(48),
	},
	label: {
		color: GLOBAL_STYLES.COLORS.TEXT_SECONDARY,
		fontSize: scale(32),
		marginBottom: scale(16),
		marginLeft: scale(2),
	},
	input: {
		backgroundColor: "#18243a",
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		paddingVertical: scale(28),
		paddingHorizontal: scale(32),
		fontSize: scale(32),
		borderWidth: scale(2),
		borderColor: "#22304a",
	},
	button: {
		backgroundColor: GLOBAL_STYLES.COLORS.ACCENT,
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		paddingVertical: scale(28),
		paddingHorizontal: 0,
		marginTop: scale(32),
		width: "100%",
		alignItems: "center",
	},
	buttonText: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(36),
		fontWeight: "bold",
		letterSpacing: scale(1),
	},
	buttonDisabled: {
		opacity: 0.5,
	},
	buttonContent: {
		flexDirection: "row",
		alignItems: "center",
		justifyContent: "center",
	},
	loadingIndicator: {
		marginRight: scale(12),
	},
	errorContainer: {
		backgroundColor: "#ff4444",
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		padding: scale(16),
		marginBottom: scale(24),
		width: "100%",
	},
	errorText: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(24),
		textAlign: "center",
	},
	inputError: {
		borderColor: "#ff4444",
		borderWidth: scale(2),
	},
	// TV focus styles for input fields and button
	inputFocused: {
		borderColor: GLOBAL_STYLES.COLORS.ACCENT, // Use accent color for focus
		borderWidth: scale(3), // Slightly thicker border when focused
		backgroundColor: "#1a2a42", // Slightly lighter background when focused
		transform: [{ scale: 1.02 }], // Subtle scale effect for focus feedback
	},
	buttonFocused: {
		backgroundColor: GLOBAL_STYLES.COLORS.ACCENT, // Brighter background when focused
		opacity: 1, // Full opacity when focused
		transform: [{ scale: 1.05 }], // Slightly larger when focused
		borderWidth: scale(2),
		borderColor: "#fff", // White border when focused
	},
	// TV-specific TextInput style - removes border/background since wrapper handles it
	tvTextInput: {
		flex: 1,
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(28),
		paddingVertical: 0,
		paddingHorizontal: 0,
		backgroundColor: "transparent",
		borderWidth: 0,
	},
});

// Set display name for debugging
LoginPage.displayName = "LoginPage";

export default LoginPage;
